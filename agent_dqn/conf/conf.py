#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


class Config:
    # The input dimension of samples on the learner from Reverb varies depending on the algorithm used.
    # learner上reverb样本的输入维度, 注意不同的算法维度不一样
    # **注意**，此项必须正确配置，应该与definition.py中的NumpyData2SampleData函数数据对齐，否则可能报样本维度错误
    SAMPLE_DIM = 2125

    # Size of observation
    # observation的维度，
    DIM_OF_OBSERVATION = 1056
    DIM_OF_ACTION_PHASE_1 = 4
    DIM_OF_ACTION_DURATION_1 = 30
    DIM_OF_ACTION_PHASE_2 = 3
    DIM_OF_ACTION_DURATION_2 = 30

    SOFTMAX = False

    # Algorithm Config
    # 算法的配置
    GAMMA = 0.9
    EPSILON = 0.1
    LR = 3e-5

    START_EPSILON_GREEDY = 0.9
    END_EPSILON_GREEDY = 0.3
    EPSILON_DECAY = 0.997
    LAMBDA = 0.75
    NUMB_HEAD = 4

    GRID_WIDTH = 26
    GRID_NUM = 20
    GRID_LENGTH = 5
    MAX_GREEN_DURATION = 40
    MAX_RED_DURATION = 60
