#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


def on_enter_lane(vehicle):
    """
    This function determines whether the vehicle is located on the enter lane

    Args:
        - vehicle
    """
    """
    此函数判断车辆是否位于进口车道上

    参数:
        - vehicle
    """
    lane_id = vehicle["lane"]
    inlane_code = {
        3: 0,
        2: 1,
        1: 2,
        0: 3,
        89: 4,
        88: 5,
        87: 6,
        86: 7,
        31: 8,
        30: 9,
        29: 10,
        28: 11,
        63: 12,
        62: 13,
        61: 14,
        60: 15,
        81: 16,
        80: 17,
        79: 18,
        78: 19,
        107: 20,
        106: 21,
        51: 22,
        50: 23,
        49: 24,
        48: 25,
    }
    if lane_id in inlane_code and vehicle["target_junction"] != -1:
        return True
    else:
        return False


def in_junction(vehicle):
    """
    This function determines whether the vehicle is located in the junction

    Args:
        - vehicle
    """
    """
    此函数判断车辆是否位于交叉口中

    参数:
        - vehicle
    """
    junction = vehicle["junction"]
    target_junction = vehicle["target_junction"]
    if junction != -1:
        return True
    else:
        return False


def on_depart_lane(vehicle):
    """
    This function determines whether the vehicle is located on the depart lane

    Args:
        - vehicle
    """
    """
    此函数判断车辆是否位于出口车道上

    参数:
        - vehicle
    """
    junction = vehicle["junction"]
    target_junction = vehicle["target_junction"]
    # Prevent vehicles in the right turn lane from being judged as being in the exit lane
    # 避免车辆在右转车道被判定为在出口车道上
    if (on_enter_lane(vehicle) or in_junction(vehicle)) or (junction == -1 and target_junction != -1):
        return False
    else:
        return True


def get_lane_code(vehicle):
    """
    This function divides each import lane into a different number of grids according to
    different rules and classifies them

    Args:
        - lane_id: The ID of the lane where the vehicle is located

    Returns:
        - inlane_code: The number assigned to the lane according to the division rule
    """
    """
    此函数将各进口车道按不同规则划分为不同数量的栅格, 并对其进行分类

    参数:
        - lane_id: 车辆所处车道的id

    返回:
        - lane_code: 根据划分规则分配给该车道的编号
    """
    lane_id = vehicle["lane"]
    lane_code = {
        3: 0,
        2: 1,
        1: 2,
        0: 3,
        89: 4,
        88: 5,
        87: 6,
        86: 7,
        31: 8,
        30: 9,
        29: 10,
        28: 11,
        63: 12,
        62: 13,
        61: 14,
        60: 15,
        81: 16,
        80: 17,
        79: 18,
        78: 19,
        107: 20,
        106: 21,
        51: 22,
        50: 23,
        49: 24,
        48: 25,
    }
    return lane_code.get(lane_id)
