#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
import os
import time
import numpy as np
from agent_dqn.conf.conf import Config
import torch.nn.functional as F


class Algorithm:
    def __init__(self, model, optimizer, device=None, logger=None, monitor=None):
        self.device = device
        self.model = model

        self.optim = optimizer
        self.logger = logger
        self.monitor = monitor

        self.num_head = Config.NUMB_HEAD
        self._gamma = Config.GAMMA

        self.last_report_monitor_time = 0
        self.train_step = 0

    def learn(self, list_sample_data):
        t_data = list_sample_data

        obs = torch.stack([frame.obs for frame in t_data])
        action = torch.stack(
            [frame.act if not torch.any(torch.isinf(frame.act)) else torch.zeros_like(frame.act) for frame in t_data]
        )
        rew = torch.stack([frame.rew for frame in t_data])
        _obs = torch.stack([frame._obs for frame in t_data])
        not_done = torch.stack([frame.done for frame in t_data])
        legal_action = torch.stack([frame.legal_action for frame in t_data])

        self.model.eval()

        with torch.no_grad():
            # Calculate the target Q-values for each head
            # 计算各个头的目标q值
            q_targets = []
            for head_idx in range(self.num_head):
                q_targets_head = (
                    rew[:, head_idx].unsqueeze(1)
                    + self._gamma * (self.model(_obs)[0][head_idx]).max(1)[0].unsqueeze(1) * not_done[:, None]
                )
                q_targets.append(q_targets_head)
            q_targets = torch.cat(q_targets, dim=1) * legal_action

        # Calculate the Q-values for each head
        # 计算各个头的q值
        self.model.train()
        q_values = []
        for head_idx in range(self.num_head):
            q_values_head = self.model(obs)[0][head_idx].gather(1, action[:, head_idx + 0].long().unsqueeze(1))
            q_values.append(q_values_head)
        q_values = torch.cat(q_values, dim=1) * legal_action

        self.optim.zero_grad()
        loss = F.mse_loss(q_targets[:, 0].float(), q_values[:, 0].float())
        for head_idx in range(1, self.num_head):
            loss += F.mse_loss(q_targets[:, head_idx].float(), q_values[:, head_idx].float())
        loss.backward()
        model_grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0).item()
        self.optim.step()
        self.train_step += 1

        value_loss = loss.detach().item()
        target_q_value = q_targets.mean().detach().item()
        q_value = q_values.mean().detach().item()

        # Periodically report monitoring
        # 按照间隔上报监控
        now = time.time()
        if now - self.last_report_monitor_time >= 60:
            monitor_data = {
                "value_loss": value_loss,
                "target_q_value": target_q_value,
                "q_value": q_value,
                "model_grad_norm": model_grad_norm,
            }
            self.monitor.put_data({os.getpid(): monitor_data})
            self.logger.info(
                f"value_loss: {value_loss}, target_q_value: {target_q_value},\
                                q_value: {q_value},\
                                model_grad_norm: {model_grad_norm}"
            )
            self.last_report_monitor_time = now
