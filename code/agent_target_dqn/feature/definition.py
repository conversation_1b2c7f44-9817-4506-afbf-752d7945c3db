#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from kaiwu_agent.utils.common_func import create_cls, attached
import numpy as np
from agent_target_dqn.conf.conf import Config
from agent_target_dqn.feature.traffic_utils import *


SampleData = create_cls(
    "SampleData",
    obs=None,
    _obs=None,
    act=None,
    rew=None,
    done=None,
    legal_action=None,
)

ObsData = create_cls("ObsData", feature=None)

ActData = create_cls("ActData", phase_index_1=None, duration_1=None, phase_index_2=None, duration_2=None)


@attached
def sample_process(list_game_data):
    r_data = np.array(list_game_data).squeeze()

    sample_datas = []
    for data in r_data:
        legal_action = [data.legal_action[1], data.legal_action[1], data.legal_action[2], data.legal_action[2]]
        sample_data = SampleData(
            obs=data.obs,
            _obs=None,
            act=data.act,
            rew=data.rew,
            done=1 if data.done == 0 else 0,
            legal_action=legal_action,
        )
        sample_datas.append(sample_data)

    for i in range(len(sample_datas) - 1):
        sample_datas[i]._obs = sample_datas[i + 1].obs
    sample_datas[-1]._obs = sample_datas[-1].obs

    if sample_datas[-1].done:
        del sample_datas[-1]

    return sample_datas


def reward_shaping(_obs, _extra_info, act, agent):
    """
    This function is an important function for reward processing, mainly responsible for:
        - Unpacking data, obtaining the data required for reward calculation from _obs
        - Reward calculation, calculating rewards based on the unpacked data
    """
    """
    该函数是奖励处理的重要函数, 主要负责：
        - 数据解包, 从 _obs 获取计算奖励所需要的数据
        - 奖励计算, 根据解包的数据计算奖励
    """
    junction_ids = [1, 2]
    phase_reward, duration_reward = {1: 0, 2: 0}, {1: 0, 2: 0}

    frame_state = _obs["framestate"]
    vehicles = frame_state["vehicles"]

    # The difference in vehicle waiting time is used as a reward
    # 车辆等待时间差值作为奖励
    for j_id in junction_ids:
        current_waiting_time = agent.preprocess.get_all_junction_waiting_time(vehicles)[j_id]
        phase_reward[j_id] = (agent.preprocess.old_waiting_time[j_id] - current_waiting_time + 1000.0) / 1000.0
        duration_reward[j_id] = (agent.preprocess.old_waiting_time[j_id] - current_waiting_time + 1000.0) / 1000.0
        agent.preprocess.old_waiting_time[j_id] = current_waiting_time

    return (
        phase_reward[1],
        duration_reward[1],
        phase_reward[2],
        duration_reward[2],
    )


# SampleData <----> NumpyData
@attached
def SampleData2NumpyData(g_data):
    return np.hstack(
        (
            np.array(g_data.obs, dtype=np.float32),
            np.array(g_data._obs, dtype=np.float32),
            np.array(g_data.act, dtype=np.float32),
            np.array(g_data.rew, dtype=np.float32),
            np.array(g_data.done, dtype=np.float32),
            np.array(g_data.legal_action, dtype=np.float32),
        )
    )


@attached
def NumpyData2SampleData(s_data):
    obs_dim = Config.DIM_OF_OBSERVATION
    return SampleData(
        obs=s_data[:obs_dim],
        _obs=s_data[obs_dim : obs_dim * 2],
        act=s_data[obs_dim * 2 : obs_dim * 2 + 4],
        rew=s_data[obs_dim * 2 + 4 : obs_dim * 2 + 8],
        done=s_data[obs_dim * 2 + 8],
        legal_action=s_data[obs_dim * 2 + 9 : obs_dim * 2 + 13],
    )
