#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch

torch.set_num_threads(1)
torch.set_num_interop_threads(1)

import os
import time
from kaiwu_agent.utils.common_func import attached
from agent_target_dqn.model.model import Model
from agent_target_dqn.feature.definition import *
import numpy as np
from kaiwu_agent.agent.base_agent import (
    BaseAgent,
    predict_wrapper,
    exploit_wrapper,
    learn_wrapper,
    save_model_wrapper,
    load_model_wrapper,
)

from agent_target_dqn.conf.conf import Config
from agent_target_dqn.algorithm.algorithm import Algorithm
from agent_target_dqn.feature.preprocessor import FeatureProcess


@attached
class Agent(BaseAgent):
    def __init__(self, agent_type="player", device=None, logger=None, monitor=None):
        self.model = Model(device=device)

        self.optim = torch.optim.RMSprop(self.model.parameters(), lr=Config.LR)
        self._eps = Config.START_EPSILON_GREEDY
        self.end_eps = Config.END_EPSILON_GREEDY
        self.eps_decay = Config.EPSILON_DECAY
        self.head_dim = [
            Config.DIM_OF_ACTION_PHASE_1,
            Config.DIM_OF_ACTION_DURATION_1,
            Config.DIM_OF_ACTION_PHASE_2,
            Config.DIM_OF_ACTION_DURATION_2,
        ]
        self.device = device
        self.epsilon = Config.EPSILON
        self.logger = logger
        self.monitor = monitor
        self.preprocess = FeatureProcess(logger)
        self.last_action = None

        self.algorithm = Algorithm(self.model, self.optim, self.device, self.logger, self.monitor)

        super().__init__(agent_type, device, logger, monitor)

    def reset(self):
        self.preprocess.reset()

    def __predict_detail(self, list_obs_data, exploit_flag=False):
        feature = [obs_data.feature for obs_data in list_obs_data]

        model = self.model
        model.eval()

        self._eps = max(self.end_eps, self._eps * self.eps_decay)
        if np.random.rand() >= self._eps or exploit_flag:
            with torch.no_grad():
                res = model(feature)[0]
                list_phase_1 = torch.argmax(res[0], dim=1).cpu().view(-1, 1).tolist()[0]
                list_duration_1 = torch.argmax(res[1], dim=1).cpu().view(-1, 1).tolist()[0]
                list_phase_2 = torch.argmax(res[2], dim=1).cpu().view(-1, 1).tolist()[0]
                list_duration_2 = torch.argmax(res[3], dim=1).cpu().view(-1, 1).tolist()[0]
        else:
            random_action = np.random.choice(self.head_dim[0], len(list_obs_data))
            list_phase_1 = random_action

            random_action = np.random.choice(self.head_dim[1], len(list_obs_data))
            list_duration_1 = random_action

            random_action = np.random.choice(self.head_dim[2], len(list_obs_data))
            list_phase_2 = random_action

            random_action = np.random.choice(self.head_dim[3], len(list_obs_data))
            list_duration_2 = random_action

        return [
            ActData(
                phase_index_1=list_phase_1[i],
                duration_1=list_duration_1[i],
                phase_index_2=list_phase_2[i],
                duration_2=list_duration_2[i],
            )
            for i in range(len(list_obs_data))
        ]

    @predict_wrapper
    def predict(self, list_obs_data):
        return self.__predict_detail(list_obs_data, exploit_flag=False)

    @exploit_wrapper
    def exploit(self, observation):
        obs_data = self.observation_process(observation["obs"], observation["extra_info"])
        if not obs_data:
            return [[None, None, None]]
        act_data = self.__predict_detail([obs_data], exploit_flag=True)
        act = self.action_process(act_data[0])
        return act

    @learn_wrapper
    def learn(self, list_sample_data):
        return self.algorithm.learn(list_sample_data)

    @save_model_wrapper
    def save_model(self, path=None, id="1"):
        # To save the model, it can consist of multiple files,
        # and it is important to ensure that each filename includes the "model.ckpt-id" field.
        # 保存模型, 可以是多个文件, 需要确保每个文件名里包括了model.ckpt-id字段
        model_file_path = f"{path}/model.ckpt-{str(id)}.pkl"

        # Copy the model's state dictionary to the CPU
        # 将模型的状态字典拷贝到CPU
        model_state_dict = self.model.state_dict()
        model_state_dict_cpu = {k: v.clone().cpu() for k, v in self.model.state_dict().items()}
        torch.save(model_state_dict_cpu, model_file_path)

        self.logger.info(f"save model {model_file_path} successfully")

    @load_model_wrapper
    def load_model(self, path=None, id="1"):
        # When loading the model, you can load multiple files,
        # and it is important to ensure that each filename matches the one used during the save_model process.
        # 加载模型, 可以加载多个文件, 注意每个文件名需要和save_model时保持一致
        model_file_path = f"{path}/model.ckpt-{str(id)}.pkl"
        self.model.load_state_dict(torch.load(model_file_path, map_location=self.model.device))

        self.logger.info(f"load model {model_file_path} successfully")

    def observation_process(self, obs, extra_info):
        # User-defined section, can record or update traffic information per frame.
        # 用户自定义部分, 可每帧对交通信息进行记录或更新
        self.preprocess.update_traffic_info(obs, extra_info)

        frame_state = obs["framestate"]

        # Parse frame_state
        # 解析 frame_state
        frame_no, frame_time, vehicles = (
            frame_state["frame_no"],
            frame_state["frame_time"],
            frame_state["vehicles"],
        )
        phases = frame_state["phases"]
        junction_1 = phases[1]
        junction_2 = phases[2]
        jct_phase_1 = [
            0,
        ] * 8
        jct_phase_2 = [
            0,
        ] * 6
        jct_phase_1[junction_1["phase_id"]] = 1
        jct_phase_2[junction_2["phase_id"]] = 1

        # Divide the lane into several grids along the lane direction and the vehicle driving direction
        # 沿车道方向和车辆行驶方向将车道划分为数个栅格
        speed_dict = np.zeros((Config.GRID_WIDTH, Config.GRID_NUM))
        position_dict = np.zeros((Config.GRID_WIDTH, Config.GRID_NUM))

        # The default value of junction_id in a double intersection scenario are 1 and 2
        # 本场景交叉口junction_id十字路口为1、T字路口为2
        junction_ids = [1, 2]

        # Initialize state-related variables to prevent errors when there are no vehicles in the traffic scenario
        # 初始化状态相关变量, 防止交通场景内车辆为空时报错
        position = list(position_dict.astype(int).flatten())
        speed = list(speed_dict.flatten())

        for vehicle in vehicles:
            # Only count vehicles on the enter lane
            # 仅统计位于进口车道上的车辆信息
            if on_enter_lane(vehicle):
                # Convert the vehicle x,y coordinates to grid coordinates. Here,
                # get_lane_code maps the lane number to integers 0-25, corresponding to 26 import lanes
                # 将车辆x,y坐标转化为栅格坐标, 此处get_lane_code将车道编号映射至整数0-25, 对应26条进口车道
                x_pos = get_lane_code(vehicle)
                y_pos = int((vehicle["position_in_lane"]["y"] / 1) // Config.GRID_LENGTH)

                if y_pos >= Config.GRID_NUM:
                    continue

                if vehicle["target_junction"] not in junction_ids:
                    continue

                speed_dict[x_pos, y_pos] = float(
                    vehicle["speed"] / self.preprocess.vehicle_configs_dict[vehicle["v_config_id"]]["max_speed"]
                )
                position_dict[x_pos, y_pos] = 1
            else:
                continue

        position = list(position_dict.astype(int).flatten())
        speed = list(speed_dict.flatten())

        # Integrate all state quantities into the feature
        # 将所有状态量整合在feature中
        feature = position + speed + [obs["legal_action"][1], obs["legal_action"][2]] + jct_phase_1 + jct_phase_2

        return ObsData(feature=feature)

    def action_process(self, act_data):
        # Action of env follow [[junction_id, phase, duration], [junction_id, phase, duration]] format, you can map agent's action to environment's action
        # 环境可采纳的动作格式为 [[junction_id, phase, duration], [junction_id, phase, duration]]，由于智能体返回的动作一般从0开始，duration从1开始，可以做一个映射
        phase_index_1 = act_data.phase_index_1
        duration_1 = act_data.duration_1 + 1
        phase_index_2 = act_data.phase_index_2
        duration_2 = act_data.duration_2 + 1
        action = [[1, phase_index_1, duration_1], [2, phase_index_2, duration_2]]
        return action
