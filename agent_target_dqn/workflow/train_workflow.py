#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import os
import time
from agent_target_dqn.feature.definition import *
from kaiwu_agent.utils.common_func import Frame, attached
from tools.train_env_conf_validate import read_usr_conf
from tools.metrics_utils import get_training_metrics


@attached
def workflow(envs, agents, logger=None, monitor=None):
    try:
        env, agent = envs[0], agents[0]
        epoch_num = 100000
        episode_num_every_epoch = 1
        last_save_model_time = 0

        # Initializing monitoring data
        # 监控数据初始化
        monitor_data = {
            "reward": 0,
            "diy_1": 0,
            "diy_2": 0,
            "diy_3": 0,
            "diy_4": 0,
            "diy_5": 0,
        }
        last_report_monitor_time = time.time()

        # Read and validate configuration file
        # 配置文件读取和校验
        usr_conf = read_usr_conf("agent_target_dqn/conf/train_env_conf.toml", logger)
        if usr_conf is None:
            logger.error(f"usr_conf is None, please check agent_target_dqn/conf/train_env_conf.toml")
            return

        for epoch in range(epoch_num):
            epoch_total_rew = 0

            data_length = 0
            for g_data in run_episodes(episode_num_every_epoch, env, agent, usr_conf, logger):
                data_length += len(g_data)
                total_rew = []
                for data in g_data:
                    total_rew.append(sum(data.rew))

                total_rew = sum(total_rew)
                epoch_total_rew += total_rew
                agent.learn(g_data)
                g_data.clear()

            avg_step_reward = 0
            if data_length:
                avg_step_reward = f"{(epoch_total_rew/data_length):.2f}"

            # save model file
            # 保存model文件
            now = time.time()
            if now - last_save_model_time >= 120:
                agent.save_model()
                last_save_model_time = now

            # Reporting training progress
            # 上报训练进度
            if now - last_report_monitor_time > 60:
                monitor_data["reward"] = avg_step_reward
                if monitor:
                    monitor.put_data({os.getpid(): monitor_data})
                    last_report_monitor_time = now

            logger.info(f"Avg Step Reward: {avg_step_reward}, Epoch: {epoch}, Data Length: {data_length}")

    except Exception as e:
        raise RuntimeError(f"workflow error")


def run_episodes(n_episode, env, agent, usr_conf, logger):
    try:
        train_test_quick_stop = os.environ.get("is_train_test", "False").lower() == "true"
        for _ in range(n_episode):
            collector = list()
            predict_cnt = 0

            # Retrieving training metrics
            # 获取训练中的指标
            training_metrics = get_training_metrics()
            if training_metrics:
                logger.info(f"training_metrics is {training_metrics}")

            # At the start of each environment, loading the latest model file
            # 每次对局开始时, 加载最新model文件
            agent.load_model(id="latest")

            # Reset the environment and get the initial state
            # 重置环境, 并获取初始状态
            obs, extra_info = env.reset(usr_conf=usr_conf)
            agent.reset()

            # Disaster recovery
            # 容灾
            if handle_disaster_recovery(extra_info, logger):
                break

            # Record the last_predict_act
            # 记录上次预测的动作
            last_predict_act = None

            done = False
            while not done:
                need_to_predict = obs["legal_action"][1] != 0 or obs["legal_action"][2] != 0
                if need_to_predict:
                    if len(collector) > 0:
                        # Calculate reward Rewards
                        # 计算奖励
                        reward = reward_shaping(obs, extra_info, last_predict_act, agent)
                        collector[-1].rew = reward

                    # Feature processing
                    # 特征处理
                    obs_data = agent.observation_process(obs, extra_info)
                    # Agent makes a prediction to get the next frame's action
                    # Agent 进行推理, 获取下一帧的预测动作
                    act_data, _ = agent.predict(list_obs_data=[obs_data])

                    # Unpack ActData into actions
                    # ActData 解包成动作
                    act = agent.action_process(act_data[0])
                    predict_cnt += 1
                else:
                    # No need to predict
                    # 不需要预测的情况
                    agent.preprocess.update_traffic_info(obs, extra_info)
                    act = [[None, None, None]]

                # Interact with the environment, execute actions, get the next state
                # 与环境交互, 执行动作, 获取下一步的状态
                frame_no, _obs, terminated, truncated, _extra_info = env.step(act)
                if handle_disaster_recovery(_extra_info, logger):
                    # If the env triggers disaster recovery and the number of samples is greater than the threshold, they are not discarded.
                    # 如果环境触发容灾且样本数量大于阈值，则不丢弃
                    if len(collector) > 10:
                        collector = sample_process(collector)
                        yield collector
                    break

                # Determine if the environment is over
                # 判断环境结束
                done = terminated or truncated or (train_test_quick_stop and len(collector) > 1)
                if truncated:
                    logger.info(f"truncated is True, frame_no is {frame_no}, so this episode timeout")
                elif terminated:
                    logger.info(f"terminated is True, frame_no is {frame_no}, so this episode reach the end")

                # Save samples only when predicting
                # 只有预测步才保存样本
                if need_to_predict:
                    # Construct environment frames to prepare for sample construction
                    # 构造环境帧，为构造样本做准备
                    frame = Frame(
                        obs=obs_data.feature,
                        act=[act[0][1], act[0][2] - 1, act[1][1], act[1][2] - 1],
                        rew=None,
                        done=0,
                        legal_action=obs["legal_action"],
                    )

                    collector.append(frame)

                # Status update
                # 状态更新
                obs = _obs
                extra_info = _extra_info
                if need_to_predict:
                    last_predict_act = act

                # Perform sample processing and return samples for training
                # 进行样本处理并将样本返回进行训练
                if done:
                    if len(collector) > 1:
                        # Calculate reward Rewards
                        # 计算奖励
                        reward = reward_shaping(obs, extra_info, last_predict_act, agent)
                        collector[-1].done = 1
                        collector[-1].rew = reward
                        collector = sample_process(collector)
                        yield collector
                    break

    except Exception as e:
        logger.error(f"run_episodes error")
        raise RuntimeError(f"run_episodes error")


def handle_disaster_recovery(extra_info, logger):
    # Handle disaster recovery logic
    # 处理容灾逻辑
    result_code, result_message = extra_info["result_code"], extra_info["result_message"]
    if result_code < 0:
        logger.error(f"Env run error, please check, result_code is {result_code}, result_message is {result_message}")
        raise RuntimeError(result_message)
    elif result_code > 0:
        return True
    return False
